from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db import models
import random
import string

def generate_random_color():
    return '#' + ''.join(random.choices(string.hexdigits[:-6], k=6))

class Employer(models.Model):
    employer_id = models.AutoField(primary_key=True)
    employer_name = models.CharField(max_length=255)
    employer_email = models.EmailField(max_length=255)
    employer_phone = models.CharField(max_length=255, null=True)
    employer_address = models.CharField(max_length=255, null=True)
    office_locations = models.CharField(max_length=255, null=True)
    employer_created_at = models.DateTimeField(auto_now_add=True)
    employer_logo_url = models.CharField(max_length=255, null=True)
    employer_banner_url = models.CharField(max_length=255, null=True)
    employer_website = models.CharField(max_length=255, null=False)
    employer_description = models.TextField(null=False)
    employer_industry = models.CharField(max_length=255, null=True)
    employer_headcount = models.IntegerField(null=True)
    employer_social_portals = models.TextField(null=True)
    employer_status = models.CharField(max_length=255, null=False, default="Active")

class Employee(models.Model):
    user = models.OneToOneField("auth.User", on_delete=models.CASCADE, primary_key=True)
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    role = models.CharField(max_length=100, null=True, default="Recruiter")
    permissions = models.JSONField(null=True)
    status = models.CharField(max_length=50, default="Active")
    created_at = models.DateTimeField(auto_now_add=True)
    profile_photo = models.TextField(null=True)

    def __str__(self):
        return f"{self.user.username} - {self.user.email}"

class Vacancy(models.Model):
    JOB_PORTAL_CHOICES = (
        ("Careers Page", "Careers Page"),
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Canvider", "Canvider"),
        ("Indeed", "Indeed"),
        ("Referral", "Referral"),
        ("Pracuj.pl", "Pracuj.pl"),
        ("JustJoinIT", "JustJoinIT"),
        ("NoFluffJobs", "NoFluffJobs"),
        ("RemoteOK", "RemoteOK"),
        ("StackOverflow", "StackOverflow"),
        ("GitHub", "GitHub"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
        ("jobloupe", "jobloupe"),
    )
    STATUS_CHOICES = (
        ("Active", _("Active")),
        ("Draft", _("Draft")),
        ("Closed", _("Closed")),
        ("On-Hold", _("On-Hold")),
        ("Archived", _("Archived")),
        ("Reviewing", _("Reviewing")),
        ("Deleted", _("Deleted")),
    )
    vacancy_id = models.AutoField(primary_key=True)
    vacancy_status = models.CharField(
        default="Reviewing",
        choices=STATUS_CHOICES,
        max_length=255,
    )
    vacancy_title = models.CharField(max_length=255)
    vacancy_country = models.CharField(max_length=255)
    vacancy_city = models.CharField(max_length=255)
    vacancy_creation_date = models.DateTimeField(auto_now_add=True)
    number_of_applicants_temp = models.IntegerField(default=0)
    vacancy_bus_unit = models.CharField(max_length=255, null=False, default="IT")
    employer_id = models.CharField(max_length=255, null=False, default=1)
    work_schedule = models.CharField(max_length=255, null=False, default="Full Time")
    office_schedule = models.CharField(max_length=255, null=False, default="Remote")
    vacancy_job_description = models.TextField(null=True)
    skills = models.JSONField(null=True)
    job_portals = models.CharField(
        max_length=255, default="jobloupe", choices=JOB_PORTAL_CHOICES
    )
    salary_min = models.FloatField(null=True)
    salary_max = models.FloatField(null=True)
    salary_currency = models.CharField(max_length=255, null=True)
    jobtags = models.CharField(max_length=255, null=True)

class PotentialEmployer(models.Model):
    id = models.AutoField(primary_key=True)
    company_name = models.CharField(max_length=255)
    contact_name = models.CharField(max_length=255)
    email = models.EmailField(max_length=255)
    phone = models.CharField(max_length=255)
    website = models.CharField(max_length=255)
    verification_info = models.TextField(null=True, max_length=500)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=255, null=False, default="Pending")


class Candidate(models.Model):
    candidate_id = models.AutoField(primary_key=True)
    candidate_firstname = models.CharField(max_length=255)
    candidate_lastname = models.CharField(max_length=255)
    candidate_email = models.EmailField(max_length=255)
    candidate_phone = models.CharField(max_length=255)
    candidate_address = models.CharField(max_length=255, null=True)
    candidate_date_of_birth = models.DateField(null=True)
    candidate_created_at = models.DateTimeField(auto_now_add=True)
    avatar_bg_color = models.CharField(max_length=7, default=generate_random_color, editable=True, null=False, unique=False)

    @property
    def full_name(self):
        return f"{self.candidate_firstname} {self.candidate_lastname}"

class TalentPool(models.Model):
    talent_id = models.AutoField(primary_key=True)
    talent_firstname = models.CharField(max_length=255, null=False)
    talent_lastname = models.CharField(max_length=255, null=False)
    talent_email = models.EmailField(max_length=255, null=False)
    talent_phone = models.CharField(max_length=255, null=True)
    talent_country = models.CharField(max_length=255, null=True)
    talent_status = models.CharField(max_length=255, null=False, default="Active")
    cv_location = models.CharField(max_length=255, null=False)
    talent_added_at = models.DateTimeField(auto_now_add=True, null=False)

class Application(models.Model):
    JOB_PORTAL_CHOICES = (
        ("Careers Page", "Careers Page"),
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Canvider", "Canvider"),
        ("Indeed", "Indeed"),
        ("Referral", "Referral"),
        ("Pracuj.pl", "Pracuj.pl"),
        ("JustJoinIT", "JustJoinIT"),
        ("NoFluffJobs", "NoFluffJobs"),
        ("RemoteOK", "RemoteOK"),
        ("StackOverflow", "StackOverflow"),
        ("GitHub", "GitHub"),
        ("PostJobFree", "PostJobFree"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
        ("jobloupe", "jobloupe"),
    )

    EDUCATION_LEVELS = (
        ("Primary Education", "Primary Education"),
        ("Lower Secondary Education", "Lower Secondary Education"),
        ("Bachelor's or Equivalent", "Bachelor's or Equivalent"),
        ("Master's or Equivalent", "Master's or Equivalent"),
        ("Doctorate or Equivalent", "Doctorate or Equivalent"),
        ("Vocational Training", "Vocational Training"),
        ("Postgraduate Education", "Postgraduate Education"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
    )

    application_id = models.AutoField(primary_key=True)
    candidate_id = models.ForeignKey(Candidate, on_delete=models.CASCADE, db_column="candidate_id")
    vacancy_id = models.ForeignKey(Vacancy, on_delete=models.CASCADE, db_column="vacancy_id")
    application_date = models.DateTimeField(auto_now_add=True)
    application_source = models.CharField(
        max_length=255, default="jobloupe", choices=JOB_PORTAL_CHOICES
    )
    application_status = models.CharField(max_length=255, null=False, default="Active")
    application_state = models.CharField(max_length=255, null=False, default="New")
    education_level = models.CharField(
        max_length=255, default="Unknown", choices=EDUCATION_LEVELS
    )
    notice_period = models.CharField(max_length=255, default="1 Month")
    current_position = models.CharField(max_length=255, default="Unknown")
    current_employer = models.CharField(max_length=255, default="Unknown")
    total_exp_years = models.FloatField(default=0.0)
    cv_location = models.CharField(max_length=255, null=True)
    score = models.IntegerField(default=-1)

class ApplicationState(models.Model):

    STATE_NAMES = [
        ("New", _("New")),
        ("Review_1", _("Review #1")),
        ("Review_2", _("Review #2")),
        ("Review_3", _("Review #3")),
        ("Review_4", _("Review #4")),
        ("Review_5", _("Review #5")),
        ("Decision", _("Ready for Decision")),
        ("Eliminated", _("Eliminated")),
        ("Offer", _("Offer Made")),
        ("Accept", _("Candidate Accepted")),
        ("Hired", _("Hired")),
        ("Reject", _("Candidate Rejected")),
    ]

    state_id = models.AutoField(primary_key=True)
    state_name = models.CharField(max_length=255, null=False, default="New", choices=STATE_NAMES)
    state_notes = models.TextField(null=True)
    state_started_at = models.DateTimeField(auto_now_add=True)
    application_id = models.ForeignKey(Application, on_delete=models.CASCADE, db_column="application_id")
    committed_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)


class ApplicationComment(models.Model):
    comment_id = models.AutoField(primary_key=True)
    application_id = models.ForeignKey(Application, on_delete=models.CASCADE, db_column="application_id")
    commented_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    comment_body = models.TextField()
    comment_date = models.DateTimeField(auto_now_add=True)

class Appointment(models.Model):
    APPOINTMENT_KINDS = (
        ("Phone Call", _("Phone Call")),
        ("Video Call", _("Video Call")),
        ("Online Interview", _("Online Interview")),
        ("Technical Assessment", _("Technical Assessment")),
        ("Final Interview", _("Final Interview")),
        ("Face to Face Interview", _("Face to Face Interview")),
        ("Office Visit", _("Office Visit")),
        ("Other", _("Other")),
    )

    appointment_kind = models.CharField(max_length=255, null=False, default="Phone Call", choices=APPOINTMENT_KINDS)
    title = models.CharField(max_length=255)
    start_time = models.DateTimeField() # time already contains the date values in GMT.
    end_time = models.DateTimeField() # time already contains the date values in GMT.
    created_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    invited_candidates = models.JSONField(null=True, blank=True)
    interviewers = models.JSONField(null=True, blank=True)
    meeting_link = models.CharField(max_length=255, null=True, blank=True)
    inform_invitees = models.BooleanField(default=True)
    meeting_status = models.CharField(max_length=255, null=False, default="Scheduled")
    color = models.IntegerField(default=1)
    vacancy_id = models.ForeignKey(Vacancy, on_delete=models.CASCADE, db_column="vacancy_id", null=True, blank=True)

    def get_duration(self):
        return self.end_time - self.start_time

    def possible_appointment_kind(self):
        return self.appointment_kind

    def __str__(self):
        return f"{self.title} ({self.appointment_kind})"

class JobTemplate(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    usage_count = models.IntegerField(default=0)

    def __str__(self):
        return self.title

class Invitation(models.Model):
    INV_STATUS_OPT = (
        ("Pending", "Pending"),
        ("Accepted", "Accepted"),
        ("Declined", "Declined"),
        ("Expired", "Expired"),
        ("Canceled", "Canceled"),
    )

    ROLE_OPTIONS = (
        ("Administrator", "Administrator"),
        ("Recruiter", "Recruiter"),
        ("Hiring Manager", "Hiring Manager"),
        ("Interviewer", "Interviewer"),
        ("Read Only", "Read Only"),
    )

    first_name = models.CharField(max_length=100, null=False)
    last_name = models.CharField(max_length=100, null=False)
    token = models.CharField(max_length=64, unique=True)
    email = models.EmailField(unique=False, null=False)
    role = models.CharField(max_length=100, choices=ROLE_OPTIONS)
    invitation_status = models.CharField(max_length=20, default='Pending', choices=INV_STATUS_OPT)
    sent_date = models.DateTimeField(auto_now_add=True)
    def default_expiry_date():
        return timezone.now() + timezone.timedelta(days=3)

    expiry_date = models.DateTimeField(default=default_expiry_date, null=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.role}"

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = ''.join(random.choices(string.ascii_letters + string.digits, k=64))
        super().save(*args, **kwargs)

    def is_expired(self):
        if self.expiry_date:
            return timezone.now() > self.expiry_date
        return False

    def set_expired(self):
        if self.is_expired():
            self.invitation_status = "Expired"
            self.save()
        else:
            self.invitation_status = self.invitation_status
            print("Invitation is not expired.")
            self.save()

    def cancel(self):
        self.invitation_status = "Canceled"
        self.save()

class TalentRequest(models.Model):
    id = models.AutoField(primary_key=True, null=False)
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    vacancy_id = models.ForeignKey(Vacancy, on_delete=models.CASCADE, db_column="vacancy_id")
    committed_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    request_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50, default='New')
    request_notes = models.TextField(null=True)
    request_type  = models.CharField(max_length=50, default='Unknown', null=True)

class WorkSchedule(models.Model):
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class OfficeSchedule(models.Model):
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class OfficeLocation(models.Model):
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    city = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    location_details = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"{self.name} - {self.city}, {self.country}"

class Department(models.Model):
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class ApplicationCvText(models.Model):
    cv_text_id = models.AutoField(primary_key=True)
    cv_text_date = models.DateTimeField(auto_now_add=True)
    cv_location = models.CharField(max_length=255, null=True)
    cv_text = models.TextField(null=True)
    application_id = models.ForeignKey(Application, on_delete=models.CASCADE, db_column="application_id")
    is_cv_analyzed = models.BooleanField(default=False)
    ai_analysis_result = models.JSONField(null=True)

    def __str__(self):
        return self.cv_text


class PortalConfigurations(models.Model):

    PORTAL_NAMES = (
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Workloupe", "Workloupe"),
        ("Himalayas", "Himalayas"),
        ("PostJobFree", "PostJobFree"),
    )

    config_id = models.AutoField(primary_key=True)
    employer_id = models.ForeignKey(Employer, on_delete=models.CASCADE, db_column="employer_id")
    portal_name = models.CharField(max_length=100, choices=PORTAL_NAMES)
    portal_url = models.CharField(max_length=255, null=True)
    portal_status = models.CharField(max_length=50, default='Inactive')
    config_json = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
